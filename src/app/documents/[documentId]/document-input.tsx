import React, { useState, useRef, useEffect } from "react";

import { LoaderIcon } from "lucide-react";
import { BsCloudCheck, BsCloudSlash } from "react-icons/bs";

import { useDebounce } from "@/hooks/use-debounce";
import { DocumentInputProps } from "@/constants/types";

import { toast } from "sonner";

import { useMutation } from "convex/react";
import { useStatus } from "@liveblocks/react";
import { api } from "../../../../convex/_generated/api";
import { useShowLoaderOwnerValidate } from "@/hooks/useOwnerLoader";
import { usePermissionValidate } from "@/hooks/useShareDocument";

export default function DocumentInput({ title, id }: DocumentInputProps) {
  const status = useStatus();
  const { shareDocument } = usePermissionValidate()!;
  const { showLoader, setShowLoader } = useShowLoaderOwnerValidate()!;
  const [value, setValue] = useState(title);
  const [isPending, setIsPending] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);

  const mutate = useMutation(api.documents.updateById);

  const debounceUpdate = useDebounce((newValue: string) => {
    if (newValue === title) return;

    setIsPending(true);

    mutate({ id, title: newValue })
      .then(() => toast.success("Document updates"))
      .catch(() => toast.error("Something went wrong"))
      .finally(() => setIsPending(false));
  });

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    setValue(newValue);

    debounceUpdate(newValue);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    setIsPending(true);

    mutate({ id, title: value })
      .then(() => {
        toast.success("Document updates");
        setIsEditing(false);
      })
      .catch(() => toast.error("Something went wrong"))
      .finally(() => setIsPending(false));
  };

  // const showLoader =
  //   isPending || status === "connecting" || status === "reconnecting";
  useEffect(() => {
    const isLoading =
      isPending || status === "connecting" || status === "reconnecting";
    setShowLoader(isLoading);
  }, [isPending, status, setShowLoader]);
  const showError = status === "disconnected";
  return (
    <div className="flex items-center gap-2">
      {isEditing ? (
        <form onSubmit={handleSubmit} className="relative w-fit max-w-[50ch]">
          <span className="invisible whitespace-pre px-1.5 text-lg">
            {value || " "}
          </span>
          <input
            ref={inputRef}
            onBlur={() => setIsEditing(false)}
            value={value}
            onChange={onChange}
            className="absolute inset-0 text-lg text-black px-1.5 bg-transparent truncate"
          />
        </form>
      ) : (
        <span
          onClick={() => {
            if (shareDocument) return;
            setIsEditing(true);
            setTimeout(() => {
              inputRef.current?.focus();
            }, 0);

            // TODO: remove this when the bug is fixed
            // setIsError(false);
            // setIsCreating(false);
            // setValue(title);
          }}
          className="text-lg px-1.5 cursor-pointer truncate"
        >
          {title}
        </span>
      )}
      {showError && <BsCloudSlash className="size-4" />}
      {!showError && !showLoader && <BsCloudCheck />}
      {showLoader && (
        <LoaderIcon className="size-4 animate-spin text-muted-foreground" />
      )}
    </div>
  );
}
