{"name": "google_doc", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.5.1", "@hookform/resolvers": "^4.1.3", "@liveblocks/client": "^2.12.2", "@liveblocks/node": "^2.12.2", "@liveblocks/react": "^2.12.2", "@liveblocks/react-tiptap": "^2.12.2", "@liveblocks/react-ui": "^2.12.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tiptap/extension-collaboration": "^2.10.2", "@tiptap/extension-collaboration-cursor": "^2.10.2", "@tiptap/extension-color": "^2.10.2", "@tiptap/extension-font-family": "^2.10.2", "@tiptap/extension-heading": "^2.10.2", "@tiptap/extension-highlight": "^2.10.2", "@tiptap/extension-image": "^2.10.2", "@tiptap/extension-link": "^2.10.2", "@tiptap/extension-table": "^2.10.2", "@tiptap/extension-table-cell": "^2.10.2", "@tiptap/extension-table-header": "^2.10.2", "@tiptap/extension-table-row": "^2.10.2", "@tiptap/extension-task-item": "^2.10.2", "@tiptap/extension-task-list": "^2.10.2", "@tiptap/extension-text-align": "^2.10.2", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-underline": "^2.10.2", "@tiptap/pm": "^2.10.2", "@tiptap/react": "^2.10.2", "@tiptap/starter-kit": "^2.10.2", "@types/react-color": "^3.0.12", "@uploadthing/react": "^7.3.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "convex": "^1.17.3", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "input-otp": "^1.4.2", "lucide-react": "^0.483.0", "next": "15.0.3", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.6", "nuqs": "^2.2.3", "react": "19.0.0-rc-66855b96-20241106", "react-color": "^2.19.3", "react-day-picker": "^8.10.1", "react-dom": "19.0.0-rc-66855b96-20241106", "react-hook-form": "^7.54.2", "react-icons": "^5.3.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tiptap-extension-resize-image": "^1.2.1", "uploadthing": "^7.7.2", "vaul": "^1.1.2", "y-protocols": "^1.0.6", "zod": "^3.24.2", "zustand": "^5.0.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}